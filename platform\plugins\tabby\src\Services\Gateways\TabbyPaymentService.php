<?php

namespace Bo<PERSON>ble\Tabby\Services\Gateways;

use <PERSON><PERSON>ble\Base\Facades\BaseHelper;
use <PERSON><PERSON>ble\Tabby\Services\Abstracts\TabbyPaymentAbstract;
use Exception;
use Illuminate\Http\Request;

class TabbyPaymentService extends TabbyPaymentAbstract
{
    public function makePayment(Request $request)
    {
        // This method is called during the checkout process
        // The actual payment creation is handled in the HookServiceProvider
        return true;
    }

    public function afterMakePayment(Request $request)
    {
        // Handle post-payment processing if needed
        return true;
    }

    /**
     * Check if valid to process checkout
     */
    public function isValidToProcessCheckout(): bool
    {
        return apply_filters('tabby_is_valid_to_process_checkout', $this->isAvailable());
    }

    /**
     * Get order notes
     */
    public function getOrderNotes(): array
    {
        return apply_filters('tabby_order_notes', []);
    }

    /**
     * Check if amount is within Tabby limits
     */
    public function isAmountValid(float $amount, string $currency = 'SAR'): bool
    {
        // Based on working implementation, <PERSON><PERSON> supports amounts between 150-5000 SAR
        // You may need to adjust these limits based on your specific requirements
        $minAmount = 150;
        $maxAmount = 5000;

        return $amount >= $minAmount && $amount <= $maxAmount;
    }

    /**
     * Create checkout session and redirect to Tabby
     */
    public function createCheckoutSession(array $data): array
    {
        try {
            // Build payment data for Tabby API
            $paymentData = $this->buildPaymentData($data);

            // Create checkout session
            $response = $this->tabbyApiService->createCheckoutSession($paymentData);

            return $response;
        } catch (Exception $exception) {
             throw $exception;
        }
    }

    /**
     * Perform pre-scoring check
     */
    public function performPreScoringCheck(array $data): array
    {
         $paymentData = $this->buildPaymentData($data);

            // Create checkout session for pre-scoring
            $response = $this->tabbyApiService->createCheckoutSession($paymentData);

            return $response;

        // try {
        //     // Build minimal payment data for pre-scoring
        //     $paymentData = $this->buildPaymentData($data);

        //     // Create checkout session for pre-scoring
        //     $response = $this->tabbyApiService->createCheckoutSession($paymentData);

        //     return $response;
        // } catch (Exception $exception) {

        //     return [
        //         'status' => 'rejected',
        //         'configuration' => [
        //             'products' => [
        //                 'installments' => [
        //                     'rejection_reason' => 'not_available'
        //                 ]
        //             ]
        //         ]
        //     ];
        // }
    }

    /**
     * Retrieve payment status
     */
    public function retrievePaymentStatus(string $paymentId): array
    {
        try {
            return $this->tabbyApiService->retrievePayment($paymentId);
        } catch (Exception $exception) {
            throw $exception;
        }
    }



    /**
     * Capture payment
     */
    public function capturePayment(string $paymentId, float $amount, ?string $referenceId = null): array
    {
        try {
            $captureData = [
                'amount' => $this->getAmount($amount),
            ];

            if ($referenceId) {
                $captureData['reference_id'] = $referenceId;
            }

            return $this->tabbyApiService->capturePayment($paymentId, $captureData);
        } catch (Exception $exception) {
            throw $exception;
        }
    }

    /**
     * Refund payment
     */
    public function refundPayment(string $paymentId, float $amount, ?string $reason = null, ?string $referenceId = null): array
    {
        try {
            $refundData = [
                'amount' => $this->getAmount($amount),
            ];

            if ($reason) {
                $refundData['reason'] = $reason;
            }

            if ($referenceId) {
                $refundData['reference_id'] = $referenceId;
            }

            return $this->tabbyApiService->refundPayment($paymentId, $refundData);
        } catch (Exception $exception) {
            throw $exception;
        }
    }

    /**
     * Redirect to Tabby checkout page
     */
    public function redirectToCheckoutPage(array $data): void
    {
        $webUrl = $data['web_url'] ?? '';

        if (empty($webUrl)) {
            throw new Exception('Invalid Tabby checkout URL');
        }

        // Redirect to Tabby hosted payment page
        header('Location: ' . $webUrl);
        exit();
    }

    /**
     * Validate webhook signature (if auth header is configured)
     */
    public function validateWebhookSignature(Request $request, ?string $expectedSignature = null): bool
    {
        if (!$expectedSignature) {
            return true; // No signature validation required
        }

        $receivedSignature = $request->header('Authorization') ?? $request->header('X-Tabby-Signature');

        return hash_equals($expectedSignature, $receivedSignature);
    }
}
