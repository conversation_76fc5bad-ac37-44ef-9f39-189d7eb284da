[2025-08-06 21:24:15] local.ERROR: Undefined array key "first_name" - D:\laragon\www\martfury\platform\plugins\tabby\src\Providers\HookServiceProvider.php:139  
[2025-08-06 21:31:19] local.ERROR: Class "SebastianBergmann\Environment\Console" not found {"exception":"[object] (Error(code: 0): Class \"SebastianBergmann\\Environment\\Console\" not found at D:\\laragon\\www\\martfury\\vendor\\nunomaduro\\collision\\src\\Adapters\\Laravel\\Commands\\TestCommand.php:188)
[stacktrace]
#0 D:\\laragon\\www\\martfury\\vendor\\nunomaduro\\collision\\src\\Adapters\\Laravel\\Commands\\TestCommand.php(227): NunoMaduro\\Collision\\Adapters\\Laravel\\Commands\\TestCommand->commonArguments()
#1 D:\\laragon\\www\\martfury\\vendor\\nunomaduro\\collision\\src\\Adapters\\Laravel\\Commands\\TestCommand.php(103): NunoMaduro\\Collision\\Adapters\\Laravel\\Commands\\TestCommand->phpunitArguments(Array)
#2 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): NunoMaduro\\Collision\\Adapters\\Laravel\\Commands\\TestCommand->handle()
#3 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#4 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#5 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#6 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#7 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#8 D:\\laragon\\www\\martfury\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#9 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#10 D:\\laragon\\www\\martfury\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 D:\\laragon\\www\\martfury\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(NunoMaduro\\Collision\\Adapters\\Laravel\\Commands\\TestCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#12 D:\\laragon\\www\\martfury\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#14 D:\\laragon\\www\\martfury\\artisan(15): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#15 {main}
"} 
[2025-08-06 21:34:04] local.ERROR: Missing required parameter for [Route: payments.tabby.callback] [URI: payment/tabby/callback/{token}] [Missing parameter: token]. - D:\laragon\www\martfury\vendor\laravel\framework\src\Illuminate\Routing\Exceptions\UrlGenerationException.php:35  
